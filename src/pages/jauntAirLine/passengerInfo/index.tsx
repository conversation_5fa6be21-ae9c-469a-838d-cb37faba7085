import { memo, useState } from 'react';
import { View, Text, ScrollView, Button } from '@tarojs/components';
import { Collapse } from '@nutui/nutui-react-taro';
import CustomHeader from '@/components/CustomHeader';
import { toUrl, getUrlParam, _localStorage, formatDate } from '@/utils';
import './index.less';
import '../selectCabin/index.less';
import { ArrowDown, ArrowRight } from '@nutui/icons-react-taro';
import BottomCtr from '@/components/BottomCtr';
import CustomTag from '@/components/CustomTag';
import FlightItem from '@/pages/jauntAirLine/components/FlightItem';
import { CommonPassengerListVO, FlightPlanBO, FlightCabinBO } from '@/api/servies/dkx';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';
import ContactInfoModal from '@/pages/jauntAirLine/components/ContactInfoModal';
import Taro from '@tarojs/taro';
import PsgCard from '@/components/PsgCard';
import useDebounce from '@/hooks/useDebounce';
import ReadTips from '@/pages/orders/components/ReadTips';

interface PassengerInfo extends CommonPassengerListVO {
  selected?: boolean;
}

interface CabinData extends FlightCabinBO {
  flightInfo?: FlightPlanBO;
}

const PassengerInfo = () => {
  const [flightInfo, setFlightInfo] = useState<FlightPlanBO>(); // 航班数据
  const [cabinData, setCabinData] = useState<CabinData>(); // 舱位数据
  const [passengers, setPassengers] = useState<PassengerInfo[]>([]); // 乘机人列表
  const [contactInfo, setContactInfo] = useState<any>({}); // 联系人信息
  // const [selectedInsurance, setSelectedInsurance] = useState('保障险');  // 保险选择 - 暂时注释
  const [agreementChecked, setAgreementChecked] = useState(false); // 协议勾选
  const [totalPrice, setTotalPrice] = useState<any>({}); // 总价
  // const [discountAmount, setDiscountAmount] = useState(80); // 优惠金额
  const [contactInfoModalVisible, setContactInfoModalVisible] = useState(false); // 联系人信息弹窗
  const [tipModalShow, setTipModalShow] = useState(false); //须知弹窗

  // 表单验证
  const isFormValid = () => {
    // 至少选择一个乘机人
    const hasSelectedPassenger = passengers?.length > 0;
    // 联系人信息完整
    const hasContactInfo = contactInfo.name && contactInfo.mobile;
    //todo 临时注释
    // return hasSelectedPassenger && hasContactInfo && agreementChecked;
    return agreementChecked;
  };

  //获取价格
  const getPrice = async (psg: any[]) => {
    let psgObj: any[] = [];
    psg?.map((item: any) => {
      psgObj.push({
        name: item?.name,
        firstName: item?.firstName,
        nextName: item?.nextName,
        birthday: item?.birthday,
      });
    });
    const { code, data } = await api.shortRouteV10PriceCreate({
      passengers: psgObj,
      flights: [{ ...flightInfo, cabin: cabinData }],
    });
    if (code === SUCCESS_CODE && data) {
      setTotalPrice(data);
    }
  };

  // 提交订单
  const handleSubmitOrder = useDebounce(async () => {
    if (!isFormValid()) return;
    const { code, data } = await api.shortRouteV10OccupyCreate({
      flights: [
        {
          depCode: flightInfo?.departAirportCode,
          arrCode: flightInfo?.arriveAirportCode,
          flightNo: flightInfo?.flightNo,
          flightDate: flightInfo?.flightDate,
          cabinCode: cabinData?.code,
          price: totalPrice?.payPrice,
        },
      ],
      passengers: passengers,
      order: { userName: contactInfo?.name, mobile: contactInfo?.mobile },
      segment: 0, //0 单程 1往返 2多程
    });
    if (code === SUCCESS_CODE && data) {
      toUrl(`/pages/jauntAirLine/payment/index?data=${JSON.stringify(data)}&pageType=jaunt`);
    }
  });
  // 获取数据
  Taro.useDidShow(() => {
    // 获取航班数据
    const flightDataStr = _localStorage.getItem('flightData');
    if (flightDataStr) {
      const parsedFlightData = JSON.parse(flightDataStr);
      setFlightInfo(parsedFlightData);
      // //获取须知
      // getTips(parsedFlightData);
    }
    // 获取舱位数据
    const cabinDataStr = getUrlParam('cabinData');
    if (cabinDataStr) {
      const parsedCabinData: CabinData = JSON.parse(cabinDataStr as string);
      setCabinData(parsedCabinData);
    }
    // 获取乘机人列表
    // fetchPassengerList();
  });
  return (
    <View className='select-cabin-page passenger-info-page '>
      <CustomHeader showBackButton={true} bgColor={'transparent'} title={'旅客信息'} />
      <View className='card-box flight-info-card'>
        <Collapse expandIcon={<ArrowDown />}>
          <Collapse.Item
            title={
              // <View className={'flight-info-title'}>
              //   <CustomTag type={'gray'}>出发</CustomTag>
              //   <View className={'flight-info-time'}>{formatDate(flightInfo?.flightDate)}</View>
              //
              //   <View className={'split-line'} />
              //   <View>
              //     {flightInfo?.departCity} - {flightInfo?.arriveCity}
              //   </View>
              // </View>
              <View>
                <View className={'flight-info-title'}>
                  <CustomTag type={'gray'}>出发</CustomTag>
                  <View className={'flight-info-time'}>{formatDate(flightInfo?.flightDate)}</View>
                </View>
                <View>
                  {flightInfo?.departCity} - {flightInfo?.arriveCity}
                </View>
              </View>
            }
          >
            <FlightItem flight={flightInfo} />
          </Collapse.Item>
        </Collapse>
      </View>
      <ScrollView className='scrollview' scrollY scrollWithAnimation enableBackToTop={true}>
        {/* 乘机人选择 */}
        <PsgCard
          passengers={passengers}
          onSelect={(data: PassengerInfo[]) => {
            if (data?.length <= 0) {
              setTotalPrice({});
              return;
            }
            setPassengers(data);
            if (data.length > 0) {
              setContactInfo({
                name: data[0]?.name || `${data[0]?.firstName || ''} ${data[0]?.nextName || ''}`,
                mobile: data[0]?.mobile,
              });
            }
            // setTotalPrice({});
            getPrice(data);
          }}
        />

        {/* 联系人信息 */}
        <View className='card-box contact-info-section'>
          <View className='section-title'>
            联系人信息 <Text className='section-subtitle'>用于接收航班信息</Text>
          </View>

          <View className='contact-info-item' onClick={() => setContactInfoModalVisible(true)}>
            <View className='contact-info-left'>
              <Text className='contact-label'>姓名</Text>
            </View>
            <View className='edit-icon'>
              <Text className='contact-value'>{contactInfo?.name}</Text>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>

          <View className='contact-info-item' onClick={() => setContactInfoModalVisible(true)}>
            <View className='contact-info-left'>
              <Text className='contact-label'>联系电话</Text>
            </View>
            <View className='edit-icon'>
              <Text className='contact-value'>+86 {contactInfo?.mobile}</Text>
              <ArrowRight color='#999' size={16} />
            </View>
          </View>
        </View>

        {/* 航空保险 - 暂时注释 */}
        {/* <View className='card-box insurance-section'>
          <View className='section-title'>航空保险</View>
          <Text className='section-subtitle'>出行有保障，人人更安心</Text>
          ...
        </View> */}

        {/*/!* 协议勾选 *!/*/}
        <ReadTips
          show={tipModalShow}
          onClose={() => setTipModalShow(false)}
          defaultData={flightInfo}
          pageType={1}
          getData={isMustRead => {
            if (isMustRead) {
              setTipModalShow(false);
              handleSubmitOrder();
            }
          }}
          onChecked={checked => setAgreementChecked(checked)}
        />
      </ScrollView>

      {/* 底部操作栏 */}
      <BottomCtr>
        {/* 卡券优惠--二期功能 */}
        {/*<View className='discount-section' onClick={() => console.log('选择优惠券')}>*/}
        {/*  <Text className='discount-label'>卡券优惠</Text>*/}
        {/*  <View className='discount-value'>*/}
        {/*    /!*<Text className='discount-amount'>-¥{discountAmount}</Text>*!/*/}
        {/*    <ArrowRight color='#999' size={16} />*/}
        {/*  </View>*/}
        {/*</View>*/}
        <View className='bottom-action'>
          <View className='price-info'>
            <Text className='price-label'>¥</Text>
            <Text className='price-value'>{totalPrice?.payPrice || 0}</Text>
            <Text className='price-detail'>明细</Text>
          </View>
          <View>
            <Button
              className={`submit-btn`}
              disabled={!isFormValid()}
              onClick={() => {
                setTipModalShow(true);
              }}
              type={'primary'}
            >
              提交订单
            </Button>
          </View>
        </View>
      </BottomCtr>
      {/*联系人信息弹窗*/}
      <ContactInfoModal
        show={contactInfoModalVisible}
        onClose={() => setContactInfoModalVisible(false)}
        defaultValues={contactInfo}
        getData={val => {
          setContactInfo(val);
        }}
      />
    </View>
  );
};

export default memo(PassengerInfo);
