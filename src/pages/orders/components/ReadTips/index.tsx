import { memo, useEffect, useRef, useState } from 'react';
import CustomActionSheet from '@/components/CustomActionSheet';
import { Text, View } from '@tarojs/components';
import { Button, Cell, Checkbox, Collapse } from '@nutui/nutui-react-taro';
import './index.less';
import { FlightPlanBO } from '@/api/servies/dkx';
import api from '@/api';
import { SUCCESS_CODE } from '@/utils/constant';
import { ArrowDown, ArrowRight } from '@nutui/icons-react-taro';

interface ReadTipsProps {
  pageType: 1 | 2 | 3; //1=短途，2=低空游览，3=包机
  show?: boolean;
  onClose?: () => void;
  onChecked?: (checked: boolean) => void;
  getData?: (isMustRead: boolean) => void;
  defaultData: any;
}

const ReadTips = ({ show, onClose, pageType, getData, defaultData, onChecked }: ReadTipsProps) => {
  const [tips, setTips] = useState<any[]>([]); //须知title列表
  const [tipModalShow, setTipModalShow] = useState(false); //须知弹窗

  const [tabList, setTabList] = useState<any[]>([]); //须知tab列表
  const [tabContent, setTabContent] = useState<any>(''); //当前tab内容

  const [isDisabled, setIsDisabled] = useState(true);
  const [countNum, setCountNum] = useState(3);
  const [agreementChecked, setAgreementChecked] = useState(false); // 协议勾选
  // const [openType, setOpenType] = useState<'submit' | 'detail'>('submit'); // 弹窗类型
  const countdownTimer = useRef<NodeJS.Timeout | null>(null);

  //获取须知title列表
  const getTips = async (flightInfo: FlightPlanBO) => {
    if (pageType === 1) {
      const { code, data } = await api.shortRouteV10FlightTipCreate({
        depCode: flightInfo?.departAirportCode,
        arrCode: flightInfo?.arriveAirportCode,
        flightNo: flightInfo?.flightNo,
        carrierCode: flightInfo?.carrier,
      });
      if (code === SUCCESS_CODE && data) {
        setTips(data);
      }
    }
    console.log(flightInfo);
  };
  //提交订单打开弹窗
  const getTabText = async () => {
    const { code, data } = await api.v10TipOrderBeforeDetail(
      pageType,
      defaultData?.carrier || defaultData?.companyCode
    );
    if (code === SUCCESS_CODE && data) {
      if (data.nodeType === 2) {
        setTabContent(data.text);
      } else if (data.nodeType === 1) {
        setTabList(data.tips || []);
      }
    }
  };
  //协议id打开弹窗
  const getTabContent = async (tabId: number) => {
    const { code, data } = await api.v10TipInfoDetail(pageType, tabId);
    if (code === SUCCESS_CODE && data) {
      if (data.nodeType === 2) {
        setTabContent(data.text);
      } else if (data.nodeType === 1) {
        setTabList(data.tips);
      }
    }
  };
  useEffect(() => {
    setTipModalShow(!!show);
    show && getTabText();
  }, [show]);
  // useEffect(() => {
  //   tipModalShow && getTabList();
  // }, [tipModalShow]);
  useEffect(() => {
    if (defaultData) {
      getTips(defaultData);
    }
  }, [defaultData]);

  const showCountDown = () => {
    // const countDown = setInterval(() => {
    //   countNum > 0 && setCountNum(countNum - 1);
    // }, 1000);
    //
    // if (countNum === 0) {
    //   clearInterval(countDown);
    //   setIsDisabled(false);
    // }
    setCountNum(3);
    countdownTimer.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          if (countdownTimer.current) {
            clearInterval(countdownTimer.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    return <Text className='timer-text'>{countNum}s</Text>;
  };
  return (
    <>
      {/* 协议勾选 */}
      <View className='agreement-section'>
        <Checkbox
          checked={agreementChecked}
          onChange={val => {
            setAgreementChecked(val);
            onChecked?.(val);
          }}
        >
          <Text className='agreement-text'>我已确认乘客信息，且认真阅读并同意</Text>
        </Checkbox>
        <View className='agreement-links'>
          {tips?.map(item => {
            return (
              <Text
                className='agreement-link'
                key={item?.id}
                onClick={() => {
                  setTipModalShow(true);
                  getTabContent(item?.id);
                }}
              >
                《{item?.tip}》
              </Text>
            );
          })}
        </View>
      </View>
      {/* 订单tips弹窗 */}
      <CustomActionSheet
        visible={tipModalShow}
        title={'温馨提示'}
        onCancel={() => {
          setTipModalShow(false);
          onClose?.();
        }}
      >
        <View className={'pay-tips pt-16 w-full'}>
          {tabList?.length > 0 ? (
            <>
              <Collapse accordion expandIcon={<ArrowDown />}>
                {tabList.map((item, index) => {
                  return (
                    <Collapse.Item
                      title={item.tipTitle}
                      name={item.tipTitle}
                      key={index}
                      onClick={() => {
                        getTabContent(item.id);
                      }}
                    >
                      <View className={'tips-content'}>{tabContent}</View>
                    </Collapse.Item>
                  );
                })}
              </Collapse>
            </>
          ) : (
            <View className={'tips-content'}>{tabContent}</View>
          )}
          {show && (
            <Button
              type={'primary'}
              className={'w-full mt-16'}
              onClick={() => {
                getData?.(true);
              }}
              disabled={isDisabled}
            >
              我已阅读并同意{showCountDown()}
            </Button>
          )}
        </View>
      </CustomActionSheet>
    </>
  );
};
export default memo(ReadTips);
